import json
import os
from datetime import datetime

class QuizManager:
    def __init__(self):
        self.quizzes = {}
        self.load_quizzes()
    
    def load_quizzes(self):
        """Load quizzes from JSON file if it exists"""
        if os.path.exists('quizzes.json'):
            with open('quizzes.json', 'r') as f:
                self.quizzes = json.load(f)
    
    def save_quizzes(self):
        """Save quizzes to JSON file"""
        with open('quizzes.json', 'w') as f:
            json.dump(self.quizzes, f, indent=4)
    
    def create_quiz(self):
        """Create a new quiz"""
        quiz_id = input("Enter quiz ID: ")
        if quiz_id in self.quizzes:
            print("Quiz ID already exists!")
            return
        
        title = input("Enter quiz title: ")
        questions = []
        
        while True:
            question = input("\nEnter question (or 'done' to finish): ")
            if question.lower() == 'done':
                break
            
            options = []
            for i in range(4):
                options.append(input(f"Enter option {i+1}: "))
            
            correct_answer = int(input("Enter the correct option number (1-4): ")) - 1
            
            questions.append({
                'question': question,
                'options': options,
                'correct_answer': correct_answer
            })
        
        self.quizzes[quiz_id] = {
            'title': title,
            'created_at': str(datetime.now()),
            'questions': questions
        }
        
        self.save_quizzes()
        print(f"\nQuiz '{title}' created successfully!")
    
    def take_quiz(self):
        """Take an existing quiz"""
        if not self.quizzes:
            print("No quizzes available!")
            return
        
        print("\nAvailable Quizzes:")
        for quiz_id, quiz in self.quizzes.items():
            print(f"{quiz_id}: {quiz['title']}")
        
        quiz_id = input("\nEnter quiz ID to take: ")
        if quiz_id not in self.quizzes:
            print("Invalid quiz ID!")
            return
        
        quiz = self.quizzes[quiz_id]
        print(f"\nQuiz: {quiz['title']}\n")
        
        score = 0
        for i, question in enumerate(quiz['questions'], 1):
            print(f"Question {i}: {question['question']}")
            for j, option in enumerate(question['options'], 1):
                print(f"  {j}. {option}")
            
            answer = int(input("Your answer (1-4): ")) - 1
            if answer == question['correct_answer']:
                print("Correct!\n")
                score += 1
            else:
                correct_option = question['correct_answer'] + 1
                print(f"Wrong! Correct answer was {correct_option}\n")
        
        total = len(quiz['questions'])
        print(f"Quiz completed! Your score: {score}/{total} ({score/total*100:.1f}%)")
    
    def list_quizzes(self):
        """List all available quizzes"""
        if not self.quizzes:
            print("No quizzes available!")
            return
        
        print("\nAvailable Quizzes:")
        for quiz_id, quiz in self.quizzes.items():
            print(f"\nID: {quiz_id}")
            print(f"Title: {quiz['title']}")
            print(f"Created: {quiz['created_at']}")
            print(f"Questions: {len(quiz['questions'])}")
    
    def delete_quiz(self):
        """Delete a quiz"""
        if not self.quizzes:
            print("No quizzes available!")
            return
        
        print("\nAvailable Quizzes:")
        for quiz_id in self.quizzes:
            print(quiz_id)
        
        quiz_id = input("\nEnter quiz ID to delete: ")
        if quiz_id not in self.quizzes:
            print("Invalid quiz ID!")
            return
        
        del self.quizzes[quiz_id]
        self.save_quizzes()
        print("Quiz deleted successfully!")
    
    def run(self):
        """Main menu loop"""
        while True:
            print("\nQuiz Manager")
            print("1. Create a new quiz")
            print("2. Take a quiz")
            print("3. List all quizzes")
            print("4. Delete a quiz")
            print("5. Exit")
            
            choice = input("\nEnter your choice (1-5): ")
            
            if choice == '1':
                self.create_quiz()
            elif choice == '2':
                self.take_quiz()
            elif choice == '3':
                self.list_quizzes()
            elif choice == '4':
                self.delete_quiz()
            elif choice == '5':
                print("Goodbye!")
                break
            else:
                print("Invalid choice. Please try again.")

if __name__ == "__main__":
    manager = QuizManager()
    manager.run()